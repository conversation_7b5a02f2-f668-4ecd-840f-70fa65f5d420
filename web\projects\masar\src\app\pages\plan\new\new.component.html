<app-page
    pageTitle="{{
        (mode === 'new'
            ? 'translate_add_new_operational_plan'
            : 'translate_update_existing_operational_plan'
        ) | translate
    }}"
>
    <!-- Tools -->
    <ng-container tools>
        <!-- List -->
        <a [routerLink]="['', 'plan']" class="btn btn-sm btn-outline-white">
            <i class="fa-light fa-share me-2"></i>
            <span>{{ 'translate_plans_list' | translate }}</span>
        </a>

        <!-- Detail -->
        <a
            *ngIf="mode === 'edit'"
            [routerLink]="[
                '',
                'plan',
                'detail',
                formState.group.controls['id'].value
            ]"
            class="btn btn-sm btn-outline-white"
        >
            <i class="fa-light fa-eye me-2"></i>
            <span>{{ 'translate_preview' | translate }}</span>
        </a>
    </ng-container>

    <!-- Content -->
    <mnm-form
        content
        *ngIf="formState"
        [state]="formState"
        [translateLabels]="true"
    >
        <div class="mt-2 text-center" *ngIf="!datesHaveChanged()">
            <button
                type="submit"
                class="btn-lg btn btn-primary"
                (click)="submit()"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
        <div
            class="mt-2 text-center"
            *ngIf="datesHaveChanged() && mode === 'edit'"
        >
            <button
                type="button"
                class="btn-lg btn btn-primary"
                (confirm)="submit()"
                [swal]="{
                    title: 'translate_periods_message' | translate,
                    confirmButtonText: 'translate_save' | translate,
                    cancelButtonText: 'translate_cancel' | translate,
                    showCancelButton: true,
                    showCloseButton: true
                }"
                [disabled]="isSubmitting"
            >
                <app-loading-ring
                    *ngIf="isSubmitting"
                    class="me-2"
                ></app-loading-ring>
                <i class="fa-light fa-save me-2"></i>
                <span>{{ 'translate_save' | translate }}</span>
            </button>
        </div>
    </mnm-form>
</app-page>

<ng-template #departmentFieldRef>
    <app-tree-select
        [formControl]="$any(formState.group.controls['assignedDepartment'])"
        [childrenFetcher]="departmentChildrenFetcher"
        [parentFetcher]="departmentParentFetcher"
        [isMultiple]="false"
    >
    </app-tree-select>
</ng-template>

<ng-template #operationListFieldRef>
    <app-item-list-field
        [formControl]="$any(formState.group.controls['operations'])"
        [parentFetcher]="operationParentFetcher"
        [childrenFetcher]="operationChildrenFetcher"
    ></app-item-list-field>
</ng-template>
<!-- Inputs -->
<ng-template #inputsFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['inputs'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="flex flex-col gap-2 md:flex-row">
                <div class="flex flex-1 flex-col justify-center gap-2">
                    <label>{{ 'translate_name' | translate }}</label>
                    <ng-select
                        [items]="planInputs"
                        bindLabel="name"
                        [ngModel]="item.planInput"
                        (ngModelChange)="item.planInput = $event; emitChange()"
                    >
                    </ng-select>
                </div>
                <div class="flex flex-1 flex-col justify-center gap-2">
                    <label>{{ 'translate_text' | translate }}</label>
                    <input
                        type="text"
                        [ngModel]="item.text"
                        (ngModelChange)="item.text = $event; emitChange()"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Resources -->
<ng-template #resourcesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['resources'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="flex flex-col gap-2 md:flex-row">
                <div class="flex flex-1 flex-col justify-center gap-2">
                    <label>{{ 'translate_name' | translate }}</label>
                    <input
                        type="text"
                        [ngModel]="item.name"
                        (ngModelChange)="item.name = $event; emitChange()"
                    />
                </div>
                <div class="flex flex-1 flex-col justify-center gap-2">
                    <label>{{ 'translate_count' | translate }}</label>
                    <input
                        type="number"
                        [ngModel]="item.count"
                        (ngModelChange)="item.count = $event; emitChange()"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Classified Resources -->
<ng-template #otherResourcesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['otherResources'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Classification -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_classification' | translate }}</label>
                    <ng-select
                        [items]="resourceClassifications"
                        bindLabel="name"
                        [(ngModel)]="item.planResourceClassification"
                        (change)="
                            loadResourcesForClassification(item); emitChange()
                        "
                    >
                    </ng-select>
                </div>

                <!-- Resource -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_resources' | translate }}</label>
                    <ng-select
                        [items]="item.availableResources || []"
                        bindLabel="name"
                        [(ngModel)]="item.planClassifiedResource"
                        [disabled]="!item.planResourceClassification"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>

                <!-- Count -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_count' | translate }}</label>
                    <input
                        type="number"
                        [(ngModel)]="item.count"
                        (ngModelChange)="emitChange()"
                    />
                </div>

                <!-- Availability -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_availability' | translate }}</label>
                    <ng-select
                        [items]="availabilityOptions"
                        bindLabel="name"
                        bindValue="id"
                        [(ngModel)]="item.isAvailable"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>

                <!-- Provisioning Mechanism -->
                <div class="flex flex-col gap-2 md:col-span-3">
                    <label>{{
                        'translate_availability_mechanism' | translate
                    }}</label>
                    <textarea
                        [(ngModel)]="item.availabilityWay"
                        (ngModelChange)="emitChange()"
                    ></textarea>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Expected outputs -->
<ng-template #expectedOutputsFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['expectedOutputs'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_type' | translate }}</label>
                    <input
                        type="text"
                        [ngModel]="item.type"
                        (ngModelChange)="item.type = $event; emitChange()"
                    />
                </div>
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_output' | translate }}</label>
                    <textarea
                        [ngModel]="item.output"
                        (ngModelChange)="item.output = $event; emitChange()"
                    ></textarea>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Lessons Learned -->
<ng-template #lessonsLearnedFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['lessonsLearned'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Description -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_description' | translate }}</label>
                    <textarea
                        [ngModel]="item.description"
                        (ngModelChange)="
                            item.description = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- How it made -->
                <div class="flex flex-col gap-2">
                    <label>{{
                        'translate_learning_procedure' | translate
                    }}</label>
                    <textarea
                        [ngModel]="item.learningProcedure"
                        (ngModelChange)="
                            item.learningProcedure = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Recommendations -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_recommendations' | translate }}</label>
                    <textarea
                        [ngModel]="item.recommendations"
                        (ngModelChange)="
                            item.recommendations = $event; emitChange()
                        "
                    ></textarea>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Challenges -->
<ng-template #challengesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['challenges'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Description -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_description' | translate }}</label>
                    <textarea
                        [ngModel]="item.description"
                        (ngModelChange)="
                            item.description = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Responsible -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_responsible' | translate }}</label>
                    <textarea
                        [ngModel]="item.responsible"
                        (ngModelChange)="
                            item.responsible = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Actions -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_actions' | translate }}</label>
                    <textarea
                        [ngModel]="item.actions"
                        (ngModelChange)="item.actions = $event; emitChange()"
                    ></textarea>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Communication processes -->
<ng-template #communicationProcessesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['communicationProcesses'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Activity -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_activity' | translate }}</label>
                    <input
                        type="text"
                        [ngModel]="item.activity"
                        (ngModelChange)="item.activity = $event; emitChange()"
                    />
                </div>

                <!-- From -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_from' | translate }}</label>

                    <div class="flex gap-1">
                        <input
                            class="flex-grow"
                            #fromDate="appFlatpickr"
                            type="date"
                            appFlatpickr
                            [(ngModel)]="item.from"
                            (flatPickrChange)="emitChange()"
                        />
                        <button
                            (click)="fromDate.clear()"
                            class="btn btn-sm btn-primary"
                        >
                            <i class="fa-light fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- To -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_to' | translate }}</label>

                    <div class="flex gap-1">
                        <input
                            class="flex-grow"
                            #toDate="appFlatpickr"
                            type="date"
                            appFlatpickr
                            [(ngModel)]="item.to"
                            (flatPickrChange)="emitChange()"
                        />
                        <button
                            (click)="toDate.clear()"
                            class="btn btn-sm btn-primary"
                        >
                            <i class="fa-light fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Communication Method -->
                <div class="flex flex-col gap-2">
                    <label>{{
                        'translate_communication_method' | translate
                    }}</label>
                    <textarea
                        [ngModel]="item.communicationMethod"
                        (ngModelChange)="
                            item.communicationMethod = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Communication Method Repetition -->
                <div class="flex flex-col gap-2">
                    <label>{{
                        'translate_communication_method_repetition' | translate
                    }}</label>
                    <textarea
                        [ngModel]="item.communicationMethodRepetition"
                        (ngModelChange)="
                            item.communicationMethodRepetition = $event;
                            emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Communication Goal -->
                <div class="flex flex-col gap-2">
                    <label>{{
                        'translate_communication_goal' | translate
                    }}</label>
                    <textarea
                        [ngModel]="item.communicationGoal"
                        (ngModelChange)="
                            item.communicationGoal = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Importance level -->
                <div class="flex flex-col gap-2">
                    <label>{{
                        'translate_importance_level' | translate
                    }}</label>
                    <input
                        type="text"
                        [ngModel]="item.importanceLevel"
                        (ngModelChange)="
                            item.importanceLevel = $event; emitChange()
                        "
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Risks -->
<ng-template #risksFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['risks'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Description -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_description' | translate }}</label>
                    <textarea
                        [ngModel]="item.description"
                        (ngModelChange)="
                            item.description = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Responsible -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_responsible' | translate }}
                    </label>
                    <textarea
                        [ngModel]="item.responsible"
                        (ngModelChange)="
                            item.responsible = $event; emitChange()
                        "
                    ></textarea>
                </div>

                <!-- Actions -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_actions' | translate }}
                    </label>
                    <textarea
                        [ngModel]="item.actions"
                        (ngModelChange)="item.actions = $event; emitChange()"
                    ></textarea>
                </div>

                <!-- Occurring Probability -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_occurring_probability' | translate }}
                    </label>

                    <ng-select
                        [items]="occurringProbabilities"
                        bindValue="id"
                        bindLabel="name"
                        [(ngModel)]="item.occurringProbability"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>

                <!-- Impact -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_impact_level' | translate }}
                    </label>

                    <ng-select
                        [items]="impacts"
                        bindValue="id"
                        bindLabel="name"
                        [(ngModel)]="item.impact"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Dependencies -->
<ng-template #dependenciesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['dependencies'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Plan -->
                <div class="col-span-3 flex flex-col gap-2">
                    <label>
                        {{ 'translate_plan' | translate }}
                    </label>

                    <ng-select
                        [items]="plans"
                        bindLabel="name"
                        [(ngModel)]="item.plan"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>

                <!-- Reason -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_reason' | translate }}</label>
                    <textarea
                        [ngModel]="item.reason"
                        (ngModelChange)="item.reason = $event; emitChange()"
                    ></textarea>
                </div>

                <!-- Relation -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_relation' | translate }}</label>
                    <textarea
                        [ngModel]="item.relation"
                        (ngModelChange)="item.relation = $event; emitChange()"
                    ></textarea>
                </div>

                <!-- Recommendations -->
                <div class="flex flex-col gap-2">
                    <label>{{ 'translate_recommendations' | translate }}</label>
                    <textarea
                        [ngModel]="item.recommendations"
                        (ngModelChange)="
                            item.recommendations = $event; emitChange()
                        "
                    ></textarea>
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Policies -->
<ng-template #policiesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['policies'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Policy -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_policies_and_procedures' | translate }}
                    </label>

                    <ng-select
                        [items]="policies"
                        bindLabel="name"
                        [(ngModel)]="item.policy"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>

                <!-- Has plan -->
                <div class="flex flex-col items-start gap-2">
                    <label>
                        {{
                            'translate_has_a_plan_been_made_for_it_qm'
                                | translate
                        }}
                    </label>

                    <input
                        type="checkbox"
                        [(ngModel)]="item.hasPlan"
                        (change)="emitChange()"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Financial Requirements -->
<ng-template #financialRequirementsFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['financialRequirements'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
                <!-- Label -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_financial_requirement' | translate }}
                    </label>

                    <input
                        type="text"
                        [(ngModel)]="item.label"
                        (change)="emitChange()"
                    />
                </div>

                <!-- Value -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_value' | translate }}
                    </label>

                    <input
                        type="number"
                        [(ngModel)]="item.value"
                        (change)="emitChange()"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Financial Stages -->
<ng-template #financialStagesFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['financialStages'])"
        [useDefaultValidItems]="true"
    >
        <ng-template
            #template
            let-item="item"
            let-index="index"
            let-emitChange="emitChange"
        >
            <div class="grid grid-cols-1 gap-2 md:grid-cols-2">
                <!-- Label -->
                <div class="flex flex-col gap-2">
                    <span>{{ 'translate_stage_number' | translate }} </span>

                    <span>{{ +index + 1 }}</span>
                </div>

                <!-- Value -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_value' | translate }}
                    </label>

                    <input
                        type="number"
                        [(ngModel)]="item.value"
                        (change)="emitChange()"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<!-- Expected benefits -->
<ng-template #expectedBenefitsFieldRef>
    <app-list-field
        [itemTemplate]="template"
        [formControl]="$any(formState.group.controls['expectedBenefits'])"
        [useDefaultValidItems]="true"
    >
        <ng-template #template let-item="item" let-emitChange="emitChange">
            <div class="grid grid-cols-1 gap-2 md:grid-cols-3">
                <!-- Description -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_description' | translate }}
                    </label>

                    <textarea
                        [(ngModel)]="item.description"
                        (change)="emitChange()"
                    ></textarea>
                </div>

                <!-- Achievement Target -->
                <div class="flex flex-col gap-2">
                    <label>
                        {{ 'translate_benefit_achievement_target' | translate }}
                    </label>

                    <ng-select
                        [items]="achievementTargetOptions"
                        bindLabel="name"
                        bindValue="id"
                        [(ngModel)]="item.achievementTarget"
                        (change)="emitChange()"
                    >
                    </ng-select>
                </div>

                <!-- Is Audited -->
                <div class="flex flex-col items-start gap-2">
                    <label>
                        {{ 'translate_is_audited_qm' | translate }}
                    </label>

                    <input
                        type="checkbox"
                        [(ngModel)]="item.isAudited"
                        (change)="emitChange()"
                    />
                </div>
            </div>
        </ng-template>
    </app-list-field>
</ng-template>

<ng-template #departmentListFieldRef>
    <div style="flex: 6 1 0">
        <label class="mb-2 inline-block">
            {{ 'translate_internal_partners' | translate }}
        </label>
        <app-tree-select
            [formControl]="
                $any(formState.group.controls['partneringDepartments'])
            "
            [childrenFetcher]="partneringDepartmentChildrenFetcher"
            [parentFetcher]="partneringDepartmentParentFetcher"
            [isMultiple]="true"
        >
        </app-tree-select>
    </div>
</ng-template>
