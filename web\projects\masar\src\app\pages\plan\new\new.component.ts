import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { MnmFormState } from '@masar/shared/components';
import { ActivatedRoute } from '@angular/router';
import { NotificationService } from 'mnm-webapp';
import { FormBuilder } from '@angular/forms';
import { fields } from './fields';
import { finalize, first, takeUntil } from 'rxjs/operators';
import { PlanService } from '../plan.service';
import { TranslateService } from '@ngx-translate/core';
import { Item, Plan } from '@masar/common/models';
import {
    AppSettingFetcherService,
    HelperService,
    MiscApiService,
} from '@masar/core/services';
import { Observable, Subject } from 'rxjs';
import { planSharedFunctions } from '@masar/features/plan-shared/plan-shared-functions';
import { Loader } from '@masar/common/misc/loader';
import { optionalFieldAndSectionHandler } from '@masar/common/utils';

@Component({
    selector: 'app-new',
    templateUrl: './new.component.html',
})
export class NewComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('departmentFieldRef') private departmentFieldRef: ElementRef;

    @ViewChild('operationListFieldRef')
    private operationListFieldRef: ElementRef;

    @ViewChild('inputsFieldRef') private inputsFieldRef: ElementRef;

    @ViewChild('resourcesFieldRef') private resourcesFieldRef: ElementRef;

    @ViewChild('expectedOutputsFieldRef')
    private expectedOutputsFieldRef: ElementRef;

    @ViewChild('lessonsLearnedFieldRef')
    private lessonsLearnedFieldRef: ElementRef;

    @ViewChild('challengesFieldRef')
    private challengesFieldRef: ElementRef;

    @ViewChild('communicationProcessesFieldRef')
    private communicationProcessesFieldRef: ElementRef;

    @ViewChild('risksFieldRef') private risksFieldRef: ElementRef;

    @ViewChild('dependenciesFieldRef') private dependenciesFieldRef: ElementRef;

    @ViewChild('policiesFieldRef') private policiesFieldRef: ElementRef;

    @ViewChild('financialRequirementsFieldRef')
    private financialRequirementsFieldRef: ElementRef;

    @ViewChild('financialStagesFieldRef')
    private financialStagesFieldRef: ElementRef;

    @ViewChild('expectedBenefitsFieldRef')
    private expectedBenefitsFieldRef: ElementRef;

    @ViewChild('departmentListFieldRef')
    private departmentListFieldRef: ElementRef;

    @ViewChild('otherResourcesFieldRef')
    private otherResourcesFieldRef: ElementRef;

    public isSubmitting = false;

    public mode: 'new' | 'edit';

    public formState: MnmFormState;

    public planInputs: Item[];

    public occurringProbabilities: Item[] = [];

    public impacts: Item[] = [];

    public plans: Item[] = [];

    public policies: Item[] = [];

    public achievementTargetOptions: Item[] = [];
    public resourceClassifications: Item[] = [];
    public availabilityOptions = [
        {
            id: true,
            name: this.translateService.instant('translate_available'),
        },
        {
            id: false,
            name: this.translateService.instant('translate_unavailable'),
        },
    ];

    public departmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;

    public departmentChildrenFetcher: (parentId: string) => Observable<Item[]>;

    public partneringDepartmentParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;

    public partneringDepartmentChildrenFetcher: (
        parentId: string
    ) => Observable<Item[]>;

    public operationParentFetcher: (
        childId: string
    ) => Observable<{ parent: Item; children: Item[] }>;
    public operationChildrenFetcher: (parentId: string) => Observable<Item[]>;

    private unsubscribeAll = new Subject();

    private planId: string;
    private oldFromDate: Date;
    private oldToDate: Date;

    public constructor(
        private notificationService: NotificationService,
        private planService: PlanService,
        private translateService: TranslateService,
        private activatedRoute: ActivatedRoute,
        private changeDetectorRef: ChangeDetectorRef,
        private miscApiService: MiscApiService,
        private readonly helperService: HelperService,
        private appSettingFetcherService: AppSettingFetcherService,
        fb: FormBuilder
    ) {
        this.formState = new MnmFormState(fields(), fb);

        this.createFetchers();
        this.loadSelectItems();
        this.monitorForm();
    }

    public ngOnInit(): void {
        this.activatedRoute.url.pipe(first()).subscribe(url => {
            switch (url[0].path) {
                case 'new':
                    this.mode = 'new';

                    // Get the auto incremented code.
                    this.planService.getNextCode().subscribe(code => {
                        this.formState.group.get('code').setValue(code);
                    });

                    break;
                case 'edit':
                    this.mode = 'edit';
                    this.activatedRoute.params
                        .pipe(first())
                        .subscribe(params => {
                            this.planId = params['id'];
                            this.planService
                                .get(this.planId, true)
                                .subscribe(item => {
                                    (item as any).assigneeType =
                                        item.assignedDepartment
                                            ? 'department'
                                            : item.assignedTeam
                                            ? 'team'
                                            : item.assignedUser
                                            ? 'user'
                                            : null;
                                    this.fillForm(item);
                                    this.oldFromDate = item.from;
                                    this.oldToDate = item.to;
                                });
                        });
                    break;
            }
        });
    }

    public ngAfterViewInit(): void {
        this.formState.get('assignedDepartment').customInputField =
            this.departmentFieldRef;

        this.formState.get('operations').customInputField =
            this.operationListFieldRef;

        this.formState.get('partneringDepartments').customField =
            this.departmentListFieldRef;

        //! A hacky way to go around the change detection
        //! shenanigan
        setTimeout(() => {
            this.updateOptionalSectionsAndFields();
            this.formState.get('inputs').customInputField = this.inputsFieldRef;
            this.formState.get('resources').customInputField =
                this.resourcesFieldRef;
            this.changeDetectorRef.detectChanges();
        }, 10);
        this.datesHaveChanged();
    }

    public ngOnDestroy(): void {
        this.unsubscribeAll.next();
        this.unsubscribeAll.complete();

        this.formState.get('kpis').loader?.dispose();
    }

    public submit(): void {
        this.formState.setTriedToSubmit();

        if (this.formState.group.invalid) {
            return;
        }

        this.isSubmitting = true;

        const value = this.formState.group.getRawValue();

        const selectedYear = value['year'];
        const selectedFromDate = new Date(value['from']);

        // min year is selectedFromDate minus 12 hours to avoid any timezone issues
        const minYearDate = new Date(
            selectedFromDate.getTime() - 12 * 60 * 60 * 1000
        );

        // max year is selectedFromDate plus 12 hours to avoid any timezone issues
        const maxYearDate = new Date(
            selectedFromDate.getTime() + 12 * 60 * 60 * 1000
        );

        // Check if the selected year is not equal to the `from` date year
        if (
            minYearDate.getFullYear() !== selectedYear &&
            maxYearDate.getFullYear() !== selectedYear
        ) {
            this.notificationService.notifyError(
                this.translateService.instant(
                    'translate_year_chosen_should_be_equal_to_from_date_year'
                )
            );
            this.isSubmitting = false;
            return;
        }
        const observable =
            this.mode === 'new'
                ? this.planService.create(value)
                : this.planService.update(value);

        observable
            .pipe(finalize(() => (this.isSubmitting = false)))
            .subscribe(plan => {
                const message =
                    this.mode === 'new'
                        ? 'translate_item_added_successfully'
                        : 'translate_item_updated_successfully';

                this.notificationService.notifySuccess(
                    this.translateService.instant(message)
                );

                this.helperService.afterSubmitNavigationHandler(
                    'ask',
                    ['', 'plan'],
                    plan.id
                );
            });
        //}
    }

    public datesHaveChanged(): boolean {
        const currentValue = this.formState.group.getRawValue();
        if (this.mode === 'new') return false;

        return (
            (this.oldFromDate &&
                new Date(this.oldFromDate).toISOString() !==
                    new Date(currentValue['from']).toISOString()) ||
            (this.oldToDate &&
                new Date(this.oldToDate).toISOString() !==
                    new Date(currentValue['to']).toISOString())
        );
    }

    public loadResourcesForClassification(item: any): void {
        if (item.planResourceClassification) {
            // Reset the resource selection
            item.planClassifiedResource = null;

            // Load resources based on the selected classification
            this.miscApiService
                .getList('plan-classified-resource', {
                    planResourceClassificationIds:
                        item.planResourceClassification.id,
                })
                .subscribe(resources => {
                    item.availableResources = resources;
                });
        } else {
            item.availableResources = [];
            item.planClassifiedResource = null;
        }
    }

    private fillForm(item: Plan): void {
        for (const key of Object.keys(this.formState.group.controls)) {
            this.formState.group.controls[key].setValue(item[key]);
        }
    }

    private updateOptionalSectionsAndFields(): void {
        this.appSettingFetcherService.get$
            .pipe(takeUntil(this.unsubscribeAll))
            .subscribe(item => {
                optionalFieldAndSectionHandler(
                    this.formState,
                    this.changeDetectorRef,
                    {
                        optionalFields: item.planSetting.optionalFields,
                        fields: [
                            [
                                'government_strategic_goal',
                                'governmentStrategicGoal',
                            ],
                            ['strategic_goals', 'strategicGoals'],
                            [
                                'ministry_strategic_goals',
                                'ministryStrategicGoals',
                            ],
                            ['future_plans', 'futurePlans'],
                            ['description', 'description'],
                            ['work_scope', 'workScope'],
                            [
                                'implementation_requirement',
                                'implementationRequirement',
                            ],
                            ['other_partners', 'otherPartners'],
                            ['internal_partners', 'partneringDepartments'],
                            ['plan_category', 'category'],
                            ['plan_kpis', 'kpis'],
                            ['partners', 'partners'],
                        ],
                    },
                    {
                        optionalSections: item.planSetting.optionalSections,
                        sections: [
                            [
                                'expected_outputs',
                                'expectedOutputsSection',
                                'expectedOutputs',
                                this.expectedOutputsFieldRef,
                            ],
                            [
                                'lessons_learned',
                                'lessonsLearnedSection',
                                'lessonsLearned',
                                this.lessonsLearnedFieldRef,
                            ],
                            [
                                'challenges',
                                'challengesSection',
                                'challenges',
                                this.challengesFieldRef,
                            ],
                            [
                                'communication_processes',
                                'communicationProcessesSection',
                                'communicationProcesses',
                                this.communicationProcessesFieldRef,
                            ],
                            [
                                'risks',
                                'risksSection',
                                'risks',
                                this.risksFieldRef,
                            ],
                            [
                                'dependencies',
                                'dependenciesSection',
                                'dependencies',
                                this.dependenciesFieldRef,
                            ],
                            [
                                'policies',
                                'policiesSection',
                                'policies',
                                this.policiesFieldRef,
                            ],
                            [
                                'financial_requirements',
                                'financialRequirementsSection',
                                'financialRequirements',
                                this.financialRequirementsFieldRef,
                            ],
                            [
                                'financial_requirements',
                                'financialRequirementsSection',
                                'financialStages',
                                this.financialStagesFieldRef,
                            ],
                            [
                                'expected_benefits',
                                'expectedBenefitsSection',
                                'expectedBenefits',
                                this.expectedBenefitsFieldRef,
                            ],
                            [
                                'operations',
                                'operationsSection',
                                'operations',
                                this.operationListFieldRef,
                            ],
                            [
                                'other_resources',
                                'otherResourcesSection',
                                'otherResources',
                                this.otherResourcesFieldRef,
                            ],
                            [
                                'resources',
                                'resourcesSection',
                                'resources',
                                this.resourcesFieldRef,
                            ],
                        ],
                    }
                );
            });
    }

    private createFetchers(): void {
        this.departmentParentFetcher = (childId: string) =>
            this.miscApiService.parentDepartment(childId);
        this.departmentChildrenFetcher = (parentId: string) =>
            this.miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
            });

        this.partneringDepartmentParentFetcher = (childId: string) =>
            this.miscApiService.parentDepartment(childId);
        this.partneringDepartmentChildrenFetcher = (parentId: string) =>
            this.miscApiService.departments({
                parentDepartmentId: parentId,
                respectHierarchy: true,
            });

        this.operationParentFetcher = (childId: string) =>
            this.miscApiService.parentOperation(childId);
        this.operationChildrenFetcher = (parentId: string) =>
            this.miscApiService.operations({
                parentOperationId: parentId,
                respectHierarchy: true,
            });
    }

    private loadSelectItems(): void {
        this.miscApiService.setMiscItems(this.formState, [
            ['ministry-strategic-goal', 'ministryStrategicGoals'],
            ['creation-year', 'year'],
            ['plan-category', 'category'],
            ['user', 'assignedUser', undefined, true],
            ['team', 'assignedTeam', undefined, true],
            ['plan-future-plan', 'futurePlans', undefined, true],
            [
                'government-strategic-goal',
                'governmentStrategicGoal',
                undefined,
                true,
            ],
            ['partner', 'partners', undefined, true],
            ['strategic-goal', 'strategicGoals', undefined, true],
        ]);

        this.miscApiService
            .getList('plan-resource-classification')
            .subscribe(items => (this.resourceClassifications = items));
        this.miscApiService
            .getList('plan', undefined, true)
            .subscribe(items => {
                this.plans = items.filter(item => item.id !== this.planId);
            });

        this.miscApiService
            .getList('plan-risk-occurring-probability')
            .subscribe(items => (this.occurringProbabilities = items));

        this.miscApiService
            .getList('plan-risk-impact')
            .subscribe(items => (this.impacts = items));

        this.miscApiService
            .getList('policy')
            .subscribe(items => (this.policies = items));

        this.miscApiService
            .getList('plan-expected-benefit-achievement-target')
            .subscribe(items => (this.achievementTargetOptions = items));

        this.miscApiService
            .getList('plan-input', undefined, true)
            .subscribe(items => (this.planInputs = items));

        this.formState.get('kpis').loader = new Loader<Item>(keyword =>
            this.miscApiService.kpis({ keyword, isLinkableToPlans: true })
        );

        planSharedFunctions.loadAssigneeTypeItems(
            this.miscApiService,
            this.formState
        );
    }

    private monitorForm(): void {
        planSharedFunctions.monitorAssigneeType(
            this.formState,
            this.unsubscribeAll
        );
    }
}
